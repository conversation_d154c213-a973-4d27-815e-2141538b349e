<?php

namespace App\Console\Commands;

use App\Jobs\ScrapeJobPostsJob;
use Illuminate\Console\Command;

class ScrapeJobPostsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:scrape-job-posts
                            {url : The URL to scrape for job posts}
                            {--example= : An example URL to match against}
                            {--queue : Dispatch as a background job instead of running immediately}
                            {--debug : Enable debug mode for more detailed output}
                            {--no-save : Do not save results to database}
                            {--wait-time=15000 : Time to wait for page loading in milliseconds}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scrape job posts from a website. Can run immediately or dispatch as a background job.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $url = $this->argument('url');
        $exampleUrl = $this->option('example');
        $useQueue = $this->option('queue');
        $debug = $this->option('debug') ?? false;
        $noSave = $this->option('no-save') ?? false;
        $waitTime = (int) $this->option('wait-time');

        // Prepare scraping options
        $scrapingOptions = [
            'debug' => $debug,
            'save_to_database' => !$noSave,
            'wait_time' => $waitTime,
            'scroll_for_dynamic_content' => true,
        ];

        $this->info("Preparing to scrape job posts from: $url");
        
        if ($exampleUrl) {
            $this->info("Using example URL for pattern matching: $exampleUrl");
        }

        if ($useQueue) {
            // Dispatch as background job
            $this->info("Dispatching job to background queue...");
            
            ScrapeJobPostsJob::dispatch($url, $exampleUrl, $scrapingOptions);
            
            $this->info("Job dispatched successfully! Check the queue status with 'php artisan horizon:status'");
            $this->info("Monitor logs with: tail -f storage/logs/laravel.log");
            
        } else {
            // Run immediately (for testing or manual execution)
            $this->info("Running job scraping immediately...");
            
            try {
                $job = new ScrapeJobPostsJob($url, $exampleUrl, $scrapingOptions);
                $job->handle();
                
                $this->info("Job scraping completed successfully!");
                
            } catch (\Exception $e) {
                $this->error("Job scraping failed: " . $e->getMessage());
                
                if ($debug) {
                    $this->error("Stack trace: " . $e->getTraceAsString());
                }
                
                return 1;
            }
        }

        return 0;
    }
}
