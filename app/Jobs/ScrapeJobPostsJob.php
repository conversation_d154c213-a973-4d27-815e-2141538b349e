<?php

namespace App\Jobs;

use App\Tools\DuskCrawler\LinkedInDusk;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Facebook\WebDriver\Exception\TimeoutException;

/**
 * Job for scraping job posts from websites
 * 
 * @property string $url
 * @property string|null $exampleUrl
 * @property array $scrapingOptions
 * @property array $extractedLinks
 */
class ScrapeJobPostsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 1800; // 30 minutes
    public $tries = 3;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public string $url,
        public ?string $exampleUrl = null,
        public array $scrapingOptions = []
    ) {
        $this->queue = 'job_scraping';
        
        // Set default options
        $this->scrapingOptions = array_merge([
            'debug' => false,
            'save_to_database' => true,
            'wait_time' => 15000, // milliseconds
            'scroll_for_dynamic_content' => true,
        ], $scrapingOptions);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting job post scraping for URL: {$this->url}");

        try {
            $this->extractedLinks = $this->scrapeJobLinks();
            
            if ($this->scrapingOptions['save_to_database']) {
                $this->saveLinksToDatabase();
            }
            
            Log::info("Job post scraping completed successfully. Found " . count($this->extractedLinks) . " links.");
            
        } catch (Exception $e) {
            Log::error("Job post scraping failed: " . $e->getMessage(), [
                'url' => $this->url,
                'exception' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * Extract job links from the website
     */
    private function scrapeJobLinks(): array
    {
        // Initialize Dusk
        $dusk = LinkedInDusk::client('scrape_job_posts');
        $dusk->start_browsing();

        try {
            // Visit the URL
            $dusk->browser->visit($this->url);
            $dusk->browser->pause($this->scrapingOptions['wait_time']);
            
            $allLinks = [];
            
            // Extract standard <a> links
            $standardLinks = $this->extractStandardLinks($dusk);
            $allLinks = array_merge($allLinks, $standardLinks);
            
            // Extract links from JavaScript event handlers
            $jsLinks = $this->extractJavaScriptLinks($dusk);
            $allLinks = array_merge($allLinks, $jsLinks);
            
            // Extract links from data attributes
            $dataLinks = $this->extractDataAttributeLinks($dusk);
            $allLinks = array_merge($allLinks, $dataLinks);
            
            // Extract job-specific elements
            $jobLinks = $this->extractJobSpecificLinks($dusk);
            $allLinks = array_merge($allLinks, $jobLinks);
            
            // Handle dynamic content if enabled
            if ($this->scrapingOptions['scroll_for_dynamic_content']) {
                $dynamicLinks = $this->extractDynamicContent($dusk);
                $allLinks = array_merge($allLinks, $dynamicLinks);
            }
            
            // Remove duplicates
            $allLinks = $this->removeDuplicateLinks($allLinks);
            
            // Filter links based on example URL if provided
            $matchingLinks = $this->exampleUrl 
                ? $this->findMatchingLinks($allLinks, $this->exampleUrl)
                : array_column($allLinks, 'url');
            
            return $matchingLinks;
            
        } finally {
            $dusk->stop_browsing();
        }
    }

    /**
     * Extract standard <a> links
     */
    private function extractStandardLinks($dusk): array
    {
        return $dusk->browser->driver->executeScript('
            return Array.from(document.querySelectorAll("a[href]")).map(a => {
                return {
                    url: a.href,
                    text: a.textContent.trim(),
                    classes: a.className,
                    id: a.id,
                    type: "a"
                };
            });
        ');
    }

    /**
     * Extract links from JavaScript event handlers
     */
    private function extractJavaScriptLinks($dusk): array
    {
        return $dusk->browser->driver->executeScript('
            function extractUrlFromJsHandler(handler) {
                if (!handler) return null;

                const patterns = [
                    /window\.open\([\'"]([^\'"]+)[\'"]/,
                    /location\.href\s*=\s*[\'"]([^\'"]+)[\'"]/,
                    /location\s*=\s*[\'"]([^\'"]+)[\'"]/,
                    /href\s*=\s*[\'"]([^\'"]+)[\'"]/,
                    /[\'"]([^\'"]+(https?:\/\/|\/)[^\'"]+)[\'"]/ // Generic URL pattern
                ];

                for (const pattern of patterns) {
                    const match = handler.match(pattern);
                    if (match && match[1]) {
                        return match[1];
                    }
                }

                return null;
            }

            const elements = document.querySelectorAll("[onclick], [onkeypress], [onkeydown], [onkeyup], [onmousedown], [role=button], [class*=button], [class*=link], [tabindex]");

            return Array.from(elements).map(el => {
                const onclick = el.getAttribute("onclick");
                const onkeypress = el.getAttribute("onkeypress");
                const onkeydown = el.getAttribute("onkeydown");
                const onkeyup = el.getAttribute("onkeyup");
                const onmousedown = el.getAttribute("onmousedown");

                let url = extractUrlFromJsHandler(onclick) ||
                          extractUrlFromJsHandler(onkeypress) ||
                          extractUrlFromJsHandler(onkeydown) ||
                          extractUrlFromJsHandler(onkeyup) ||
                          extractUrlFromJsHandler(onmousedown);

                if (url) {
                    return {
                        url: url,
                        text: el.textContent.trim(),
                        classes: el.className,
                        id: el.id,
                        type: "js-handler",
                        element: el.tagName.toLowerCase(),
                        handler: onclick || onkeypress || onkeydown || onkeyup || onmousedown
                    };
                }

                return null;
            }).filter(item => item !== null);
        ');
    }

    /**
     * Extract links from data attributes
     */
    private function extractDataAttributeLinks($dusk): array
    {
        return $dusk->browser->driver->executeScript('
            const elements = document.querySelectorAll("[data-url], [data-href], [data-link], [data-target]");

            return Array.from(elements).map(el => {
                const dataUrl = el.getAttribute("data-url");
                const dataHref = el.getAttribute("data-href");
                const dataLink = el.getAttribute("data-link");
                const dataTarget = el.getAttribute("data-target");

                let url = null;
                if (dataUrl && (dataUrl.startsWith("http") || dataUrl.startsWith("/"))) {
                    url = dataUrl;
                } else if (dataHref && (dataHref.startsWith("http") || dataHref.startsWith("/"))) {
                    url = dataHref;
                } else if (dataLink && (dataLink.startsWith("http") || dataLink.startsWith("/"))) {
                    url = dataLink;
                } else if (dataTarget && (dataTarget.startsWith("http") || dataTarget.startsWith("/"))) {
                    url = dataTarget;
                }

                if (url) {
                    return {
                        url: url,
                        text: el.textContent.trim(),
                        classes: el.className,
                        id: el.id,
                        type: "data-attribute",
                        element: el.tagName.toLowerCase()
                    };
                }

                return null;
            }).filter(item => item !== null);
        ');
    }

    /**
     * Extract job-specific elements
     */
    private function extractJobSpecificLinks($dusk): array
    {
        return $dusk->browser->driver->executeScript('
            const jobSelectors = [
                ".job-listing", ".job-title", ".vacancy", ".position",
                "[class*=job]", "[class*=career]", "[class*=vacancy]",
                ".project-title", ".position-title"
            ];

            const results = [];

            function extractUrlFromElement(element) {
                const onclick = element.getAttribute("onclick");
                const onkeypress = element.getAttribute("onkeypress");

                if (onclick && onclick.includes("window.open")) {
                    const match = onclick.match(/window\.open\([\'"]([^\'"]+)[\'"]/) ||
                                  onclick.match(/[\'"]([^\'"]+(https?:\/\/|\/)[^\'"])+[\'"]/)
                    if (match && match[1]) {
                        return match[1];
                    }
                }

                if (onkeypress && onkeypress.includes("window.open")) {
                    const match = onkeypress.match(/window\.open\([\'"]([^\'"]+)[\'"]/) ||
                                  onkeypress.match(/[\'"]([^\'"]+(https?:\/\/|\/)[^\'"])+[\'"]/)
                    if (match && match[1]) {
                        return match[1];
                    }
                }

                let parent = element.parentElement;
                for (let i = 0; i < 3 && parent; i++) {
                    const parentLink = parent.querySelector("a[href]");
                    if (parentLink) {
                        return parentLink.href;
                    }

                    const parentOnclick = parent.getAttribute("onclick");
                    if (parentOnclick && parentOnclick.includes("window.open")) {
                        const match = parentOnclick.match(/window\.open\([\'"]([^\'"]+)[\'"]/) ||
                                      parentOnclick.match(/[\'"]([^\'"]+(https?:\/\/|\/)[^\'"])+[\'"]/)
                        if (match && match[1]) {
                            return match[1];
                        }
                    }

                    parent = parent.parentElement;
                }

                return null;
            }

            for (const selector of jobSelectors) {
                const elements = document.querySelectorAll(selector);

                for (const element of elements) {
                    const url = extractUrlFromElement(element);

                    if (url) {
                        results.push({
                            url: url,
                            text: element.textContent.trim(),
                            classes: element.className,
                            id: element.id,
                            type: "job-element",
                            element: element.tagName.toLowerCase()
                        });
                    }
                }
            }

            return results;
        ');
    }

    /**
     * Extract dynamic content after scrolling
     */
    private function extractDynamicContent($dusk): array
    {
        // Scroll down to trigger lazy loading
        $dusk->browser->driver->executeScript('window.scrollTo(0, document.body.scrollHeight)');
        $dusk->browser->pause(2000);

        // Get additional links after scrolling
        return $dusk->browser->driver->executeScript('
            return Array.from(document.querySelectorAll("a[href]")).map(a => {
                return {
                    url: a.href,
                    text: a.textContent.trim(),
                    classes: a.className,
                    id: a.id,
                    type: "a-after-scroll"
                };
            });
        ');
    }

    /**
     * Remove duplicate URLs from links array
     */
    private function removeDuplicateLinks(array $allLinks): array
    {
        $uniqueUrls = [];
        $uniqueLinks = [];

        foreach ($allLinks as $link) {
            if (!isset($uniqueUrls[$link['url']])) {
                $uniqueUrls[$link['url']] = true;
                $uniqueLinks[] = $link;
            }
        }

        return $uniqueLinks;
    }

    /**
     * Find links that match patterns from the example URL
     */
    private function findMatchingLinks(array $links, string $exampleUrl): array
    {
        $patterns = $this->extractPatternsFromUrl($exampleUrl);
        $matchingLinks = [];

        foreach ($links as $link) {
            $url = $link['url'];

            // Skip empty or javascript: links
            if (empty($url) || strpos($url, 'javascript:') === 0 || $url === '#') {
                continue;
            }

            // Check if the URL matches any of the patterns
            if ($this->urlMatchesPatterns($url, $patterns)) {
                $matchingLinks[] = $url;
            }
        }

        return array_unique($matchingLinks);
    }

    /**
     * Extract patterns from a URL to use for matching
     */
    private function extractPatternsFromUrl(string $url): array
    {
        $patterns = [];
        $parsedUrl = parse_url($url);

        // Host pattern (domain)
        if (isset($parsedUrl['host'])) {
            $patterns['host'] = $parsedUrl['host'];
        }

        // Path pattern
        if (isset($parsedUrl['path'])) {
            $path = $parsedUrl['path'];
            $patterns['path_structure'] = preg_replace('/\/[^\/]+$/', '/(*)', $path);

            // Extract path segments
            $pathSegments = explode('/', trim($path, '/'));
            if (!empty($pathSegments)) {
                // Get the directory structure (all but last segment)
                if (count($pathSegments) > 1) {
                    $patterns['directory'] = '/' . implode('/', array_slice($pathSegments, 0, -1)) . '/';
                }

                // Get the last segment (usually the page name or ID)
                $lastSegment = end($pathSegments);

                // Check if it's a numeric ID
                if (is_numeric($lastSegment)) {
                    $patterns['numeric_id'] = preg_replace('/\/\d+$/', '/(*)', $path);
                }
                // Check if it's a slug (contains hyphens)
                else if (strpos($lastSegment, '-') !== false) {
                    $patterns['slug_pattern'] = preg_replace('/\/[a-z0-9\-]+$/', '/(*)', $path);
                }

                // Extract file extension if present
                if (preg_match('/\.([a-zA-Z0-9]+)$/', $lastSegment, $matches)) {
                    $patterns['file_extension'] = '.' . $matches[1];
                }
            }
        }

        // Query parameters pattern
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $queryParams);

            // Extract parameter names
            $patterns['query_params'] = array_keys($queryParams);

            // Look for ID parameters
            foreach ($queryParams as $param => $value) {
                if (stripos($param, 'id') !== false || is_numeric($value)) {
                    $patterns['id_param'] = $param;
                    break;
                }
            }
        }

        return $patterns;
    }

    /**
     * Check if a URL matches the patterns extracted from the example URL
     */
    private function urlMatchesPatterns(string $url, array $patterns): bool
    {
        $parsedUrl = parse_url($url);

        // Skip URLs without a host or path
        if (!isset($parsedUrl['host']) || !isset($parsedUrl['path'])) {
            return false;
        }

        // Check host (domain) - must match
        if (isset($patterns['host']) && $parsedUrl['host'] !== $patterns['host']) {
            return false;
        }

        // Check path structure
        if (isset($patterns['path_structure'])) {
            $pathPattern = str_replace('(*)', '[^/]+', $patterns['path_structure']);
            if (!preg_match('#^' . $pathPattern . '$#', $parsedUrl['path'])) {
                // If the exact structure doesn't match, check the directory
                if (!isset($patterns['directory']) || strpos($parsedUrl['path'], $patterns['directory']) !== 0) {
                    // If directory doesn't match either, check for numeric ID or slug pattern
                    $matchesPattern = false;

                    if (isset($patterns['numeric_id'])) {
                        $numericPattern = str_replace('(*)', '\d+', $patterns['numeric_id']);
                        if (preg_match('#^' . $numericPattern . '$#', $parsedUrl['path'])) {
                            $matchesPattern = true;
                        }
                    }

                    if (!$matchesPattern && isset($patterns['slug_pattern'])) {
                        $slugPattern = str_replace('(*)', '[a-z0-9\-]+', $patterns['slug_pattern']);
                        if (preg_match('#^' . $slugPattern . '$#', $parsedUrl['path'])) {
                            $matchesPattern = true;
                        }
                    }

                    if (!$matchesPattern) {
                        return false;
                    }
                }
            }
        }

        // Check file extension if present
        if (isset($patterns['file_extension']) && !preg_match('/' . preg_quote($patterns['file_extension'], '/') . '$/', $parsedUrl['path'])) {
            return false;
        }

        // Check query parameters
        if (isset($patterns['query_params']) && isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $queryParams);

            // Check for ID parameter
            if (isset($patterns['id_param'])) {
                if (!isset($queryParams[$patterns['id_param']])) {
                    return false;
                }
            }

            // Check for at least one matching parameter
            $hasMatchingParam = false;
            foreach ($patterns['query_params'] as $param) {
                if (isset($queryParams[$param])) {
                    $hasMatchingParam = true;
                    break;
                }
            }

            if (!$hasMatchingParam && !empty($patterns['query_params'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Save extracted links to database (integrate with existing JobScraper system)
     */
    private function saveLinksToDatabase(): void
    {
        Log::info("Extracted job links", [
            'url' => $this->url,
            'links_count' => count($this->extractedLinks),
            'links' => $this->extractedLinks
        ]);

        // If company_id is provided in scraping options, try to integrate with JobScraper
        if (isset($this->scrapingOptions['company_id'])) {
            $this->integrateWithJobScraperSystem();
        }
    }

    /**
     * Integrate with existing JobScraper system
     */
    private function integrateWithJobScraperSystem(): void
    {
        try {
            $companyId = $this->scrapingOptions['company_id'];

            // Find or create JobScraper for this company
            $jobScraper = \App\Models\JobScraper::firstOrCreate(
                ['company_id' => $companyId],
                [
                    'latest_jobs_link' => $this->url,
                    'status' => \App\Enums\JobScraperStatusEnum::IDLE,
                    'is_active' => true,
                ]
            );

            // Update the latest jobs link and last scraped time
            $jobScraper->update([
                'latest_jobs_link' => $this->url,
                'last_scraped_at' => now(),
                'status' => \App\Enums\JobScraperStatusEnum::IDLE,
            ]);

            // Create job records for each extracted link
            foreach ($this->extractedLinks as $link) {
                // Check if job already exists to avoid duplicates
                $existingJob = \App\Models\Job::where('link', $link)
                    ->where('job_scraper_id', $jobScraper->id)
                    ->first();

                if (!$existingJob) {
                    \App\Models\Job::create([
                        'link' => $link,
                        'title' => $this->extractTitleFromLink($link),
                        'status' => \App\Enums\JobStatusEnum::SCRAPED,
                        'job_scraper_id' => $jobScraper->id,
                        'posting_company_id' => $companyId,
                        'is_approved' => false,
                    ]);
                }
            }

            Log::info("Successfully integrated with JobScraper system", [
                'job_scraper_id' => $jobScraper->id,
                'company_id' => $companyId,
                'new_jobs_created' => count($this->extractedLinks)
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to integrate with JobScraper system: " . $e->getMessage(), [
                'company_id' => $this->scrapingOptions['company_id'] ?? null,
                'exception' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Extract a basic title from a job link (fallback method)
     */
    private function extractTitleFromLink(string $link): string
    {
        // Basic title extraction from URL
        $path = parse_url($link, PHP_URL_PATH);
        $segments = explode('/', trim($path, '/'));
        $lastSegment = end($segments);

        // Convert URL-friendly format to readable title
        $title = str_replace(['-', '_'], ' ', $lastSegment);
        $title = ucwords($title);

        return $title ?: 'Job Post';
    }
}
