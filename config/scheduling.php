<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Campaign
    |--------------------------------------------------------------------------
    |
    | These tasks is working around the campaign tasks to be executed and
    | controlled.
    |
    */

    'campaigns' => [

        'execute_steps' => env('SCHEDULING_CAMPAIGNS_EXECUTE_STEPS', false),

        'update_status' => env('SCHEDULING_CAMPAIGNS_UPDATE_STATUS', false),

        'verify_recruiters' => env('SCHEDULING_VERIFY_HEADHUNTERS', false),

    ],

    /*
    |--------------------------------------------------------------------------
    | Job Scraping
    |--------------------------------------------------------------------------
    |
    | These tasks control automated job post scraping from various websites.
    |
    */

    'job_scraping' => [

        'scrape_job_posts' => env('SCHEDULING_JOB_SCRAPING_SCRAPE_POSTS', false),

    ],

    'subscriptions' => [

        'inactivate_cancelled' => env('SCHEDULING_SUBSCRIPTIONS_INACTIVATE_CANCELLED', false),

        'periods_generate' => env('SCHEDULING_SUBSCRIPTIONS_PERIODS_GENERATE', false),

        'payments_generate' => env('SCHEDULING_SUBSCRIPTIONS_PAYMENTS_GENERATE', false),

        'payments_charge' => env('SCHEDULING_SUBSCRIPTIONS_PAYMENTS_CHARGE', false),

    ]

];
