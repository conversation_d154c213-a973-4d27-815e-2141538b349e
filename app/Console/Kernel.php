<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('inspire')
            ->appendOutputTo(storage_path('logs/inspire.log'))
            ->everyMinute();

        $schedule->command('horizon:snapshot')
            ->appendOutputTo(storage_path('logs/horizon-snapshot.log'))
            ->everyMinute();

        if ( config('scheduling.campaigns.execute_steps') ) {

            $schedule->command('app:campaign-execute-steps')
                ->appendOutputTo(storage_path('logs/campaign-execute-steps.log'))
                ->hourly()
                ->between('08:00', '17:00');

        }

        if ( config('scheduling.campaigns.update_status') ) {

            $schedule->command('campaigns:update-status')
                ->appendOutputTo(storage_path('logs/campaigns-update-status.log'))
                ->everyFiveMinutes()
                ->between('08:00', '18:15');

        }

//        if ( config('scheduling.campaigns.verify_recruiters') ) {
//
//            $schedule->command('app:verify-recruiters-command')
//                ->appendOutputTo(storage_path('logs/verify-recruiters-command.log'))
//                ->hourlyAt(rand(0, 59))
//                ->between('19:00', '20:00');
//
//        }

        if ( config('scheduling.subscriptions.charge_recurring') ) {

            $schedule->command('subscriptions:cancel-accordingly')
                ->at('00:30')
                ->runInBackground();

        }

        if ( config('scheduling.subscriptions.periods_generate') ) {

            $schedule->command('periods:generate')
                ->at('01:00')
                ->runInBackground();

        }

        if ( config('scheduling.subscriptions.payments_generate') ) {

            $schedule->command('payments:generate')
                ->at('01:30')
                ->runInBackground();

        }

        // Job scraping scheduled tasks
        if ( config('scheduling.job_scraping.scrape_job_posts') ) {

            // Automated job scraping for all companies with job listing websites
            $schedule->command('app:scheduled-job-scraping')
                ->appendOutputTo(storage_path('logs/scheduled-job-scraping.log'))
                ->dailyAt('06:00')
                ->runInBackground();

            // Optional: Run a second scraping session in the evening
            $schedule->command('app:scheduled-job-scraping')
                ->appendOutputTo(storage_path('logs/scheduled-job-scraping-evening.log'))
                ->dailyAt('18:00')
                ->runInBackground();

        }

        if ( config('scheduling.subscriptions.payments_charge') ) {

            $schedule->command('payments:charge')
                ->at('02:00')
                ->runInBackground();

        }

    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
