<?php

namespace App\Jobs;

use App\Enums\JobScraperStatusEnum;
use App\Models\Job;
use App\Models\JobScraper;
use App\Models\JobScraperLog;
use App\Tools\Scraper\Scraper;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use App\Services\JobScraperHandler;

/**
 * Class JobScraperStartArchiveScrapingJob
 * @package App\Jobs
 *
 * @Property JobScraper $job_scraper
 * @Property string $html_jobs
 * @Property array $elements
 * @Property string $element
 * @Property string $html_single_job
 * @Property string $link
 * @Property Exception $exception
 * @Property string $job_title
 * @Property string $deadline
 * @Property Job $job
 * @Property string $resume
 * @Property string $location
 */
class JobScraperStartArchiveScrapingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private JobScraperHandler $scraperHandler;

    /**
     * Create a new job instance.
     */
    public function __construct(public JobScraper $job_scraper)
    {
        $this->queue = 'job_scraper_start_archive_scraping';
        $this->scraperHandler = new JobScraperHandler($job_scraper);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->scraperHandler->handle();
        \Illuminate\Support\Facades\Log::info('Job scraping completed successfully.', [
            'job_scraper_id' => $this->job_scraper->id,
            'company' => $this->job_scraper->company->name ?? 'Unknown'
        ]);
    }
    
}
