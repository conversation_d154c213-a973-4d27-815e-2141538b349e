<?php

namespace App\Console\Commands;

use App\Jobs\ScrapeJobPostsJob;
use App\Models\Company;
use App\Models\JobScraper;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class ScheduledJobScrapingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:scheduled-job-scraping
                            {--company-id= : Scrape jobs for a specific company}
                            {--force : Force scraping even if recently scraped}
                            {--dry-run : Show what would be scraped without actually doing it}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scheduled job scraping that integrates with existing JobScraper system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyId = $this->option('company-id');
        $force = $this->option('force');
        $dryRun = $this->option('dry-run');

        $this->info('Starting scheduled job scraping...');

        // Get companies that need scraping
        $companies = $this->getCompaniesForScraping($companyId, $force);

        if ($companies->isEmpty()) {
            $this->info('No companies need scraping at this time.');
            return 0;
        }

        $this->info("Found {$companies->count()} companies to scrape:");

        foreach ($companies as $company) {
            $this->line("- {$company->name} (ID: {$company->id})");
            
            if ($company->job_scraper) {
                $lastScraped = $company->job_scraper->last_scraped_at 
                    ? $company->job_scraper->last_scraped_at->diffForHumans() 
                    : 'Never';
                $this->line("  Last scraped: {$lastScraped}");
            }
        }

        if ($dryRun) {
            $this->info('Dry run completed. No jobs were dispatched.');
            return 0;
        }

        // Dispatch scraping jobs
        $jobsDispatched = 0;
        foreach ($companies as $company) {
            if ($this->dispatchScrapingJobForCompany($company)) {
                $jobsDispatched++;
            }
        }

        $this->info("Successfully dispatched {$jobsDispatched} scraping jobs.");
        
        return 0;
    }

    /**
     * Get companies that need job scraping
     */
    private function getCompaniesForScraping($companyId = null, $force = false)
    {
        $query = Company::query()
            ->whereNotNull('website_job_listings')
            ->where('website_job_listings', '!=', '')
            ->with('job_scraper');

        // Filter by specific company if provided
        if ($companyId) {
            $query->where('id', $companyId);
        }

        // If not forcing, only get companies that haven't been scraped recently
        if (!$force) {
            $query->where(function ($q) {
                $q->whereDoesntHave('job_scraper')
                  ->orWhereHas('job_scraper', function ($scraperQuery) {
                      $scraperQuery->where(function ($sq) {
                          $sq->whereNull('last_scraped_at')
                            ->orWhere('last_scraped_at', '<', Carbon::now()->subHours(24));
                      });
                  });
            });
        }

        return $query->get();
    }

    /**
     * Dispatch scraping job for a company
     */
    private function dispatchScrapingJobForCompany(Company $company): bool
    {
        try {
            $url = $company->website_job_listings;
            
            // You can customize scraping options based on company or job scraper settings
            $scrapingOptions = [
                'debug' => false,
                'save_to_database' => true,
                'wait_time' => 15000,
                'scroll_for_dynamic_content' => true,
                'company_id' => $company->id, // Pass company ID for database integration
            ];

            // If the company has a job scraper with specific settings, use those
            if ($company->job_scraper) {
                $jobScraper = $company->job_scraper;
                
                // Update last scraped timestamp
                $jobScraper->update(['last_scraped_at' => Carbon::now()]);
                
                // You could use job scraper specific settings here
                // For example, if you have custom selectors or wait times
            }

            // Dispatch the job
            ScrapeJobPostsJob::dispatch($url, null, $scrapingOptions);
            
            $this->info("Dispatched scraping job for: {$company->name}");
            
            return true;
            
        } catch (\Exception $e) {
            $this->error("Failed to dispatch job for {$company->name}: " . $e->getMessage());
            return false;
        }
    }
}
